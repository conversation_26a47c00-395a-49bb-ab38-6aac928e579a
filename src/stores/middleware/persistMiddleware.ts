import AsyncStorage from '@react-native-async-storage/async-storage';
import { StateCreator, StoreMutatorIdentifier } from 'zustand';

// Types for the persist middleware
export interface PersistOptions<T> {
  name: string;
  storage?: AsyncStorage;
  partialize?: (state: T) => Partial<T>;
  onRehydrateStorage?: (state: T) => ((state?: T, error?: Error) => void) | void;
  version?: number;
  migrate?: (persistedState: any, version: number) => T | Promise<T>;
  merge?: (persistedState: any, currentState: T) => T;
  skipHydration?: boolean;
}

export interface PersistStorage {
  getItem: (name: string) => string | null | Promise<string | null>;
  setItem: (name: string, value: string) => void | Promise<void>;
  removeItem: (name: string) => void | Promise<void>;
}

// AsyncStorage adapter for React Native
const asyncStorageAdapter: PersistStorage = {
  getItem: async (name: string) => {
    try {
      const value = await AsyncStorage.getItem(name);
      return value;
    } catch (error) {
      console.error(`Error getting item ${name} from AsyncStorage:`, error);
      return null;
    }
  },
  setItem: async (name: string, value: string) => {
    try {
      await AsyncStorage.setItem(name, value);
    } catch (error) {
      console.error(`Error setting item ${name} in AsyncStorage:`, error);
    }
  },
  removeItem: async (name: string) => {
    try {
      await AsyncStorage.removeItem(name);
    } catch (error) {
      console.error(`Error removing item ${name} from AsyncStorage:`, error);
    }
  },
};

// Default merge function
const defaultMerge = <T>(persistedState: any, currentState: T): T => ({
  ...currentState,
  ...persistedState,
});

// Persist middleware implementation
export const persist = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  Mcs extends [StoreMutatorIdentifier, unknown][] = []
>(
  config: StateCreator<T, Mps, Mcs>,
  options: PersistOptions<T>
): StateCreator<T, Mps, Mcs> => {
  return (set, get, api) => {
    const {
      name,
      storage = asyncStorageAdapter,
      partialize = (state) => state,
      onRehydrateStorage,
      version = 0,
      migrate,
      merge = defaultMerge,
      skipHydration = false,
    } = options;

    let hasHydrated = false;
    const listeners = new Set<() => void>();

    // Create the store
    const store = config(
      (...args) => {
        set(...args);
        // Persist state after each update
        const state = get();
        const stateToStore = partialize(state);
        const serializedState = JSON.stringify({
          state: stateToStore,
          version,
        });
        storage.setItem(name, serializedState);
      },
      get,
      api
    );

    // Hydration function
    const hydrate = async () => {
      if (hasHydrated) return;

      try {
        const storedValue = await storage.getItem(name);
        if (storedValue) {
          const parsedValue = JSON.parse(storedValue);
          let migratedState = parsedValue.state;

          // Handle migration if version mismatch
          if (parsedValue.version !== version && migrate) {
            migratedState = await migrate(parsedValue.state, parsedValue.version);
          }

          // Merge persisted state with current state
          const currentState = get();
          const mergedState = merge(migratedState, currentState);
          
          set(mergedState as T, true);
        }
      } catch (error) {
        console.error(`Error hydrating store ${name}:`, error);
      } finally {
        hasHydrated = true;
        // Notify listeners that hydration is complete
        listeners.forEach(listener => listener());
      }
    };

    // Start hydration if not skipped
    if (!skipHydration) {
      hydrate();
    }

    // Add persist methods to the store
    const persistedStore = {
      ...store,
      persist: {
        setOptions: (newOptions: Partial<PersistOptions<T>>) => {
          Object.assign(options, newOptions);
        },
        clearStorage: async () => {
          await storage.removeItem(name);
        },
        rehydrate: () => hydrate(),
        hasHydrated: () => hasHydrated,
        onHydrate: (listener: () => void) => {
          listeners.add(listener);
          return () => listeners.delete(listener);
        },
        getOptions: () => options,
      },
    };

    // Call onRehydrateStorage if provided
    if (onRehydrateStorage) {
      const callback = onRehydrateStorage(get());
      if (callback) {
        const unsubscribe = persistedStore.persist.onHydrate(() => {
          callback(get());
          unsubscribe();
        });
      }
    }

    return persistedStore;
  };
};

// Utility function to create a persisted store
export const createPersistedStore = <T>(
  config: StateCreator<T>,
  persistOptions: PersistOptions<T>
) => {
  return persist(config, persistOptions);
};

// Selective persistence helper
export const createSelectivePersist = <T>(
  keys: (keyof T)[]
): PersistOptions<T>['partialize'] => {
  return (state: T) => {
    const persistedState: Partial<T> = {};
    keys.forEach(key => {
      if (key in state) {
        persistedState[key] = state[key];
      }
    });
    return persistedState;
  };
};

// Migration helper
export const createMigration = <T>(
  migrations: Record<number, (state: any) => T | Promise<T>>
): PersistOptions<T>['migrate'] => {
  return async (persistedState: any, version: number) => {
    let migratedState = persistedState;
    const currentVersion = Math.max(...Object.keys(migrations).map(Number));
    
    for (let v = version + 1; v <= currentVersion; v++) {
      if (migrations[v]) {
        migratedState = await migrations[v](migratedState);
      }
    }
    
    return migratedState;
  };
};

export default persist;
