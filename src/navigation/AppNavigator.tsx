import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import { RootStackParamList, MainTabParamList } from '../types';
import ObjectivesScreen from '../screens/objectives/ObjectivesScreen';
import { useState } from 'react';

const RootStack = createNativeStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
    return (
        <>
            <MainTab.Navigator
                screenOptions={
                    {
                        headerShown: false,
                        tabBarShowLabel: false,
                        tabBarStyle: {
                            backgroundColor: '#fff',
                            borderTopWidth: 0,
                            elevation: 0,
                        },
                    }
                }
                >
                <MainTab.Screen
                    name="Objectives"
                    component={ObjectivesScreen}
                    options={{
                        title: "Objectives"
                    }}
                />
            </MainTab.Navigator>
        </>
    )
}

const AppNavigator: React.FC = () => {
    const [isInitializing, setIsInitializing] = useState(true);
    const [initializationError, setInitializationError] = useState<string | null>(
        null,
    );
    
}